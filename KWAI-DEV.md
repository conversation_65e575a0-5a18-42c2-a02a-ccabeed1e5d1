
安装编辑器
```
nvm use
npm install
```

执行打包任务
CMD+Shift+B

安装 ide agent
1. 下载 https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-1.0.11.tar.gz 或其它形式的本地包
2. 解压后找到自己电脑对应的版本，复制至 extensions/kwaipilot/local-agent
```
extensions/kwaipilot/local-agent
├── bin
│   └── rg
├── build
│   └── Release
│       └── node_sqlite3.node
├── error.log
├── kwaipilot-binary
├── lancedb.darwin-x64.node
└── package.json
```
3. [本地包发版记录](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAz0n0svoYArZgokEI6pcsZ?ro=false)


编译 react 视图
```
sh build-react.sh
```

编译完产物后同步到 out 目录
```
# 项目根目录执行
npx gulp copy-kwaipilot-assets
```

启动ide
```
./scripts/code.sh --open-devtools
```

执行命令忽略 submodule 修改
```
git config submodule.src/vs/kwaipilot.ignore all
```

遇到  code: 403 (you may be rate limited)
```
GITHUB_TOKEN=**************************************** ./scripts/code.sh --open-devtools
```

调试打包后的app，显示包内容，进入到Contents/MacOS 目录内
./Electron --inspect  --inspect-brk --open-devtools

调试打包后的app & 插件进程，显示包内容，进入到Contents/MacOS 目录内

./Electron --inspect  --inspect-brk --open-devtools --inspect-extensions=5870

调试插件进程
./scripts/code.sh --inspect-extensions=5870 --inspect-brk-extensions=5870

调试主进程
./scripts/code.sh --inspect  --inspect-brk

调试欢迎窗口
(export DEV_WELCOME=true;./scripts/code.sh)

(export DEV_WELCOME=true;./scripts/code.sh --inspect  --inspect-brk)

输出日志
ELECTRON_ENABLE_LOGGING=1 ELECTRON_LOG_ASAR_READS=1 ./Electron


# 仅构建
./build.sh

# 签名，不公证
./build.sh true

# 签名并公证
./build.sh true true

# 不签名，不公证（显式指定）
./build.sh false false

# 构建并签名（自动从钥匙串获取证书）
./build.sh --sign

# 构建并签名（指定证书）
./build.sh --sign --identity="Developer ID Application: Your Name (TEAMID)"

# 构建、签名并公证
./build.sh --sign --notarize --apple-id="<EMAIL>" --password="app-specific-password" --team-id="TEAMID"


### webview 改造

目录：src/vs/kwaipilot/webview-ui

运行在renderProcess 进程中；
纯视图相关通过 src/vs/kwaipilot/webview-ui/src/mount.tsx 导出给 viewpane 渲染；
视图所有的外部交互逻辑都通过 kwaiPilotBridgeAPI 实现；

- 编译时对源码中依赖的 bridge 替换为渲染进程和插件进程通信的方式；
- 渲染进程内无法加载远程图片，编译时下载到本地加载
- 源码编译后 `npx gulp copy-kwaipilot-assets` 同步到 out 目录下正确的位置下加载运行；

### 视图初始化时就会触发的bridge
kwaipilot.bridge.getOpenTabFiles：获取当前已打开的文件
kwaipilot.bridge.getActiveEditor：获取当前文件路径和仓库路径
kwaipilot.bridge.printLogger：webview日志输出到插件
kwaipilot.bridge.getWorkspaceUri： 获取工作区地址
kwaipilot.bridge.getSystemInfo： os信息
kwaipilot.bridge.getState：获取状态
kwaipilot.bridge.getAndWatchUserInfo：监听用户信息变化
kwaipilot.bridge.webviewBridgeReady：通知插件webview已经ready
kwaipilot.bridge.getConfig：获取相关设置
kwaipilot.bridge.getAndWatchEditorConfig：获取并监听 编辑区配置
kwaipilot.bridge.readDirectory：读取目录相关方法

初始化在 App 和 RepoChatService中执行以上相关方法；

### 纯插件逻辑

目录：src/vs/kwaipilot

运行在 extensionHost 进程中， 涉及和ide-agent 交互、代码续写、设置等纯插件逻辑
新增 src/vs/kwaipilot/src/extension-export.ts 编译导出 extensions/kwaipilot/out 目录下；默认vscode 会自动加载 extensions下的插件作为内置插件运行；

- 编译时对源码中依赖的 bridge 替换为插件进程和渲染进程通信的方式；

### ide-agent

目录：src/vs/kwaipilot/local-agent （出于性能考虑vscode编辑器内屏蔽了文件，需要打开系统文件目录可以看到）

安装 ide agent
1. 下载 https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-1.0.5.tar.gz 或其它形式的本地宝
2. 解压后找到自己电脑对应的版本，将目录下内容复制到 src/vs/kwaipilot/local-agent

编译 src/core/agent/CoreBinaryMessenger.ts 到out目录加载运行，负责插件进程和ide-agent 进程交互核心逻辑

### kwaipilot contrib

src/vs/workbench/contrib/kwaipilot/browser/aiAssistant.contribution.ts

改为内置渲染的入口



