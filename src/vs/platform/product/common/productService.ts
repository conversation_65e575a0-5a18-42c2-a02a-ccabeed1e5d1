/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { IProductConfiguration } from '../../../base/common/product.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { createDecorator } from '../../instantiation/common/instantiation.js';

export const IProductService = createDecorator<IProductService>('productService');

export interface IProductService extends Readonly<IProductConfiguration> {

	readonly _serviceBrand: undefined;

}

export const productSchemaId = 'vscode://schemas/vscode-product';

export const EXTENSIONS_EXTENSION_MARKET_URL = 'extensions.extensionMarketUrl';

export const DEFAULT_EXTENSION_GALLERY_URL = 'https://marketplace.visualstudio.com/_apis/public/gallery';
export const DEFAULT_EXTENSION_GALLERY_ITEM_URL = 'https://marketplace.visualstudio.com/items';

export const OPEN_VSX_EXTENSION_GALLERY_URL = 'https://open-vsx.org/vscode/gallery';
export const OPEN_VSX_EXTENSION_GALLERY_ITEM_URL = 'https://open-vsx.org/vscode/item';

/** serviceUrl 以 settings 中的配置为准 */
export function processProductService(productService: IProductService, configurationService: IConfigurationService): IProductService {
	const extensionServiceUrl = configurationService.getValue<string>(EXTENSIONS_EXTENSION_MARKET_URL);
	return Object.assign({}, productService, {
		extensionsGallery: {
			...(productService.extensionsGallery || {}),
			// 去除末尾的斜杠
			serviceUrl: (extensionServiceUrl || productService.extensionsGallery?.serviceUrl || DEFAULT_EXTENSION_GALLERY_URL).replace(/\/$/, ''),
			itemUrl: extensionServiceUrl !== DEFAULT_EXTENSION_GALLERY_URL ? OPEN_VSX_EXTENSION_GALLERY_ITEM_URL : DEFAULT_EXTENSION_GALLERY_ITEM_URL
		}
	});
}
