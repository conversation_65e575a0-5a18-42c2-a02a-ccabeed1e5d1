/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICodeEditor } from '../../../browser/editorBrowser.js';
import { IContextKey, IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { IMarkerService, MarkerSeverity } from '../../../../platform/markers/common/markers.js';
import { Range } from '../../../common/core/range.js';

/**
 * 上下文键：光标是否在错误标记上
 */
export const CURSOR_ON_ERROR_MARKER = new RawContextKey<boolean>('cursorOnErrorMarker', false, {
	type: 'boolean',
	description: '光标是否位于错误或警告标记上'
});

/**
 * 管理标记相关的上下文键
 */
export class MarkerHoverContextKeysManager extends Disposable {
	public static readonly ID = 'editor.contrib.MarkerHoverContextKeysManager';
	private readonly _cursorOnErrorMarker: IContextKey<boolean>;

	constructor(
		private readonly _editor: ICodeEditor,
		@IContextKeyService contextKeyService: IContextKeyService,
		@IMarkerService private readonly _markerService: IMarkerService,
	) {
		super();

		this._cursorOnErrorMarker = CURSOR_ON_ERROR_MARKER.bindTo(contextKeyService);

		// 监听光标位置变化
		this._register(this._editor.onDidChangeCursorPosition(() => {
			this._updateCursorOnErrorMarker();
		}));

		// 监听模型变化
		this._register(this._editor.onDidChangeModel(() => {
			this._updateCursorOnErrorMarker();
		}));

		// 监听标记变化
		this._register(this._markerService.onMarkerChanged(() => {
			this._updateCursorOnErrorMarker();
		}));

		// 初始更新
		this._updateCursorOnErrorMarker();
	}

	/**
	 * 更新"光标在错误标记上"的上下文键
	 */
	private _updateCursorOnErrorMarker(): void {
		if (!this._editor.hasModel()) {
			this._cursorOnErrorMarker.set(false);
			return;
		}

		const model = this._editor.getModel();
		const position = this._editor.getPosition();

		if (!position) {
			this._cursorOnErrorMarker.set(false);
			return;
		}

		// 获取当前文件的错误和警告标记
		const markers = this._markerService.read({
			resource: model.uri,
			severities: MarkerSeverity.Error | MarkerSeverity.Warning
		});

		// 检查光标位置是否在任何错误或警告标记范围内
		const isOnErrorMarker = markers.some(marker => {
			const markerRange = new Range(
				marker.startLineNumber,
				marker.startColumn,
				marker.endLineNumber,
				marker.endColumn
			);
			return markerRange.containsPosition(position);
		});

		this._cursorOnErrorMarker.set(isOnErrorMarker);
	}
}
