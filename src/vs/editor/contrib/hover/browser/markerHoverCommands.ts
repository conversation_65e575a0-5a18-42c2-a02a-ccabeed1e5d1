/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { ICodeEditor } from '../../../browser/editorBrowser.js';
import { EditorAction2, ServicesAccessor } from '../../../browser/editorExtensions.js';
import { EditorContextKeys } from '../../../common/editorContextKeys.js';
import { Range } from '../../../common/core/range.js';
import * as nls from '../../../../nls.js';
import { registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { IMarkerService, MarkerSeverity } from '../../../../platform/markers/common/markers.js';
import { CURSOR_ON_ERROR_MARKER } from './markerHoverContextKeys.js';
import { getFixMessage } from './markerHoverParticipant.js';
// eslint-disable-next-line
import { ISideActionService } from '../../../../workbench/contrib/kwaipilot/common/sideActionService.js';

/**
 * Fix in Chat 命令
 */
class FixInChatAction extends EditorAction2 {
	constructor() {
		super({
			id: 'workbench.action.chat.fixInChat',
			title: nls.localize2('fixInChat.title', 'Fix in Chat'),
			category: nls.localize2('chat.category', 'Chat'),
			precondition: ContextKeyExpr.and(
				EditorContextKeys.editorTextFocus,
				CURSOR_ON_ERROR_MARKER
			),
			keybinding: {
				weight: KeybindingWeight.ExternalExtension,
				primary: KeyMod.Shift | KeyMod.CtrlCmd | KeyCode.KeyD,
				when: ContextKeyExpr.and(
					EditorContextKeys.editorTextFocus,
					CURSOR_ON_ERROR_MARKER
				)
			}
		});
	}

	async runEditorCommand(accessor: ServicesAccessor, editor: ICodeEditor): Promise<void> {
		const sideActionService = accessor.get(ISideActionService);
		const markerService = accessor.get(IMarkerService);

		const model = editor.getModel();
		const position = editor.getPosition();

		if (!model || !position) {
			return;
		}

		// 获取当前位置的错误标记
		const markers = markerService.read({
			resource: model.uri,
			severities: MarkerSeverity.Error | MarkerSeverity.Warning
		});

		// 找到光标位置对应的标记
		const currentMarker = markers.find(marker => {
			const markerRange = new Range(
				marker.startLineNumber,
				marker.startColumn,
				marker.endLineNumber,
				marker.endColumn
			);
			return markerRange.containsPosition(position);
		});

		if (!currentMarker) {
			return;
		}

		sideActionService.executeAction(
			'kwaipilot.composer.modifyCurrentPrompt',
			getFixMessage(currentMarker.message)
		);
	}
}

// 注册命令
registerAction2(FixInChatAction);
