/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as dom from '../../../../base/browser/dom.js';
import { isNonEmptyArray } from '../../../../base/common/arrays.js';
import { CancelablePromise, createCancelablePromise, disposableTimeout } from '../../../../base/common/async.js';
import { onUnexpectedError } from '../../../../base/common/errors.js';
import { Disposable, DisposableStore, IDisposable, toDisposable } from '../../../../base/common/lifecycle.js';
import { basename } from '../../../../base/common/resources.js';
import { ICodeEditor } from '../../../browser/editorBrowser.js';
import { EditorOption } from '../../../common/config/editorOptions.js';
import { Range } from '../../../common/core/range.js';
import { CodeActionTriggerType } from '../../../common/languages.js';
import { IModelDecoration, ITextModel } from '../../../common/model.js';
import { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';
import { IMarkerDecorationsService } from '../../../common/services/markerDecorations.js';
import { ApplyCodeActionReason, getCodeActions, quickFixCommandId } from '../../codeAction/browser/codeAction.js';
import { CodeActionController } from '../../codeAction/browser/codeActionController.js';
import { CodeActionKind, CodeActionSet, CodeActionTrigger, CodeActionTriggerSource } from '../../codeAction/common/types.js';
import { MarkerController, NextMarkerAction } from '../../gotoError/browser/gotoError.js';
import { HoverAnchor, HoverAnchorType, IEditorHoverParticipant, IEditorHoverRenderContext, IHoverPart, IRenderedHoverPart, IRenderedHoverParts, RenderedHoverParts } from './hoverTypes.js';
import * as nls from '../../../../nls.js';
import { ITextEditorOptions } from '../../../../platform/editor/common/editor.js';
import { IMarker, IMarkerData, MarkerSeverity } from '../../../../platform/markers/common/markers.js';
import { IOpenerService } from '../../../../platform/opener/common/opener.js';
import { Progress } from '../../../../platform/progress/common/progress.js';
import { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';
// eslint-disable-next-line
import { ISideActionService } from '../../../../workbench/contrib/kwaipilot/common/sideActionService.js';

export function getFixMessage(message: string) {
	const fixMessage = `For the code present, we get this error:
\`\`\`
${message}
\`\`\`
How can I resolve this? If you propose a fix, please make it concise.`;

	return fixMessage;
}

const $ = dom.$;

export class MarkerHover implements IHoverPart {

	constructor(
		public readonly owner: IEditorHoverParticipant<MarkerHover>,
		public readonly range: Range,
		public readonly marker: IMarker,
	) { }

	public isValidForHoverAnchor(anchor: HoverAnchor): boolean {
		return (
			anchor.type === HoverAnchorType.Range
			&& this.range.startColumn <= anchor.range.startColumn
			&& this.range.endColumn >= anchor.range.endColumn
		);
	}
}

const markerCodeActionTrigger: CodeActionTrigger = {
	type: CodeActionTriggerType.Invoke,
	filter: { include: CodeActionKind.QuickFix },
	triggerAction: CodeActionTriggerSource.QuickFixHover
};

export class MarkerHoverParticipant implements IEditorHoverParticipant<MarkerHover> {

	public readonly hoverOrdinal: number = 1;

	private recentMarkerCodeActionsInfo: { marker: IMarker; hasCodeActions: boolean } | undefined = undefined;

	constructor(
		private readonly _editor: ICodeEditor,
		@IMarkerDecorationsService private readonly _markerDecorationsService: IMarkerDecorationsService,
		@IOpenerService private readonly _openerService: IOpenerService,
		@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,
		@IKeybindingService private readonly _keybindingService: IKeybindingService,
		@ISideActionService private readonly sideActionService: ISideActionService,
	) { }

	public computeSync(anchor: HoverAnchor, lineDecorations: IModelDecoration[]): MarkerHover[] {
		if (!this._editor.hasModel() || anchor.type !== HoverAnchorType.Range && !anchor.supportsMarkerHover) {
			return [];
		}

		const model = this._editor.getModel();
		const lineNumber = anchor.range.startLineNumber;
		const maxColumn = model.getLineMaxColumn(lineNumber);
		const result: MarkerHover[] = [];
		for (const d of lineDecorations) {
			const startColumn = (d.range.startLineNumber === lineNumber) ? d.range.startColumn : 1;
			const endColumn = (d.range.endLineNumber === lineNumber) ? d.range.endColumn : maxColumn;

			const marker = this._markerDecorationsService.getMarker(model.uri, d);
			if (!marker) {
				continue;
			}

			const range = new Range(anchor.range.startLineNumber, startColumn, anchor.range.startLineNumber, endColumn);
			result.push(new MarkerHover(this, range, marker));
		}

		return result;
	}

	public renderHoverParts(context: IEditorHoverRenderContext, hoverParts: MarkerHover[]): IRenderedHoverParts<MarkerHover> {
		if (!hoverParts.length) {
			return new RenderedHoverParts([]);
		}
		const renderedHoverParts: IRenderedHoverPart<MarkerHover>[] = [];
		hoverParts.forEach(hoverPart => {
			const renderedMarkerHover = this._renderMarkerHover(hoverPart);
			context.fragment.appendChild(renderedMarkerHover.hoverElement);
			renderedHoverParts.push(renderedMarkerHover);
		});
		const markerHoverForStatusbar = hoverParts.length === 1 ? hoverParts[0] : hoverParts.sort((a, b) => MarkerSeverity.compare(a.marker.severity, b.marker.severity))[0];
		const disposables = this._renderMarkerStatusbar(context, markerHoverForStatusbar);
		return new RenderedHoverParts(renderedHoverParts, disposables);
	}

	public getAccessibleContent(hoverPart: MarkerHover): string {
		return hoverPart.marker.message;
	}

	private _renderMarkerHover(markerHover: MarkerHover): IRenderedHoverPart<MarkerHover> {
		const disposables: DisposableStore = new DisposableStore();

		// 创建包装容器
		const wrapperElement = $('div.marker-hover-wrapper');

		// 创建原有的悬停内容
		const hoverElement = $('div.hover-row');
		const markerElement = dom.append(hoverElement, $('div.marker.hover-contents'));
		const { source, message, code, relatedInformation } = markerHover.marker;

		this._editor.applyFontInfo(markerElement);
		const messageElement = dom.append(markerElement, $('span'));
		messageElement.style.whiteSpace = 'pre-wrap';
		messageElement.innerText = message;

		if (source || code) {
			// Code has link
			if (code && typeof code !== 'string') {
				const sourceAndCodeElement = $('span');
				if (source) {
					const sourceElement = dom.append(sourceAndCodeElement, $('span'));
					sourceElement.innerText = source;
				}
				const codeLink = dom.append(sourceAndCodeElement, $('a.code-link'));
				codeLink.setAttribute('href', code.target.toString(true));

				disposables.add(dom.addDisposableListener(codeLink, 'click', (e) => {
					this._openerService.open(code.target, { allowCommands: true });
					e.preventDefault();
					e.stopPropagation();
				}));

				const codeElement = dom.append(codeLink, $('span'));
				codeElement.innerText = code.value;

				const detailsElement = dom.append(markerElement, sourceAndCodeElement);
				detailsElement.style.opacity = '0.6';
				detailsElement.style.paddingLeft = '6px';
			} else {
				const detailsElement = dom.append(markerElement, $('span'));
				detailsElement.style.opacity = '0.6';
				detailsElement.style.paddingLeft = '6px';
				detailsElement.innerText = source && code ? `${source}(${code})` : source ? source : `(${code})`;
			}
		}

		if (isNonEmptyArray(relatedInformation)) {
			for (const { message, resource, startLineNumber, startColumn } of relatedInformation) {
				const relatedInfoContainer = dom.append(markerElement, $('div'));
				relatedInfoContainer.style.marginTop = '8px';
				const a = dom.append(relatedInfoContainer, $('a'));
				a.innerText = `${basename(resource)}(${startLineNumber}, ${startColumn}): `;
				a.style.cursor = 'pointer';
				disposables.add(dom.addDisposableListener(a, 'click', (e) => {
					e.stopPropagation();
					e.preventDefault();
					if (this._openerService) {
						const editorOptions: ITextEditorOptions = { selection: { startLineNumber, startColumn } };
						this._openerService.open(resource, {
							fromUserGesture: true,
							editorOptions
						}).catch(onUnexpectedError);
					}
				}));
				const messageElement = dom.append<HTMLAnchorElement>(relatedInfoContainer, $('span'));
				messageElement.innerText = message;
				this._editor.applyFontInfo(messageElement);
			}
		}

		// 将悬停内容添加到包装容器
		dom.append(wrapperElement, hoverElement);

		// 创建 Fix in Chat 按钮容器（平级添加）
		const fixButtonsContainer = this._createFixInChatButton(markerHover, disposables);
		if (fixButtonsContainer) {
			dom.append(wrapperElement, fixButtonsContainer);
		}

		const renderedHoverPart: IRenderedHoverPart<MarkerHover> = {
			hoverPart: markerHover,
			hoverElement: wrapperElement, // 返回包装容器而不是原来的 hoverElement
			dispose: () => disposables.dispose()
		};
		return renderedHoverPart;
	}

	/**
	 * 创建 Fix in Chat 按钮容器（按照HTML结构）
	 */
	private _createFixInChatButton(markerHover: MarkerHover, disposables: DisposableStore): HTMLElement | null {
		const marker = markerHover.marker;
		const model = this._editor.getModel();

		// 检查是否应该显示 Fix in Chat 按钮
		if (!this._shouldShowFixInChatButton(marker, model)) {
			return null;
		}

		// 获取快捷键显示文本
		const keybinding = this._keybindingService.lookupKeybinding('workbench.action.chat.fixInChat');
		const keybindingLabel = keybinding ? keybinding.getLabel() : '⇧⌘D';

		// 创建按钮容器结构
		const fixButtonsContainer = $('div.fix-buttons-container');

		fixButtonsContainer.style.cssText = `
			margin-left: 8px;
		`;

		// 创建按钮行
		const fixButtonsRow = dom.append(fixButtonsContainer, $('div.fix-buttons-row'));

		// 创建Fix in Chat按钮
		const fixButton = dom.append(fixButtonsRow, $('button.fix-in-composer.fix-in-composer-button-hover'));
		fixButton.title = 'Cmd+click to fix in new chat';
		fixButton.style.cssText = `
			border: none;
			border-radius: 2px;
			cursor: pointer;
			padding: 4px 8px;
			font-size: 11px;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			user-select: none;
			width: 150px;
			background-color: var(--vscode-button-background);
			color: var(--vscode-button-foreground);
			margin-right: 8px;
		`;

		// 创建按钮文本
		const buttonText = dom.append(fixButton, $('span.text'));
		buttonText.textContent = `Fix in Chat (${keybindingLabel})`;

		// 创建提示文本
		const fixButtonsHint = dom.append(fixButtonsContainer, $('div.fix-buttons-hint'));
		fixButtonsHint.textContent = '⌘+click to open in new tab';

		// 添加点击事件监听器
		disposables.add(dom.addDisposableListener(fixButton, 'click', (e) => {
			e.preventDefault();
			e.stopPropagation();

			if (e.metaKey || e.ctrlKey) {

				this.sideActionService.executeAction(
					'kwaipilot.composer.openNewChatWithPrompt',
					getFixMessage(marker.message)
				);

			} else {
				this.sideActionService.executeAction(
					'kwaipilot.composer.modifyCurrentPrompt',
					getFixMessage(marker.message)
				);
			}
		}));

		// 添加悬停效果
		disposables.add(dom.addDisposableListener(fixButton, 'mouseenter', () => {
			fixButton.style.opacity = '0.9';
		}));

		disposables.add(dom.addDisposableListener(fixButton, 'mouseleave', () => {
			fixButton.style.opacity = '1';
		}));

		return fixButtonsContainer;
	}

	/**
	 * 检查是否应该显示 Fix in Chat 按钮
	 */
	private _shouldShowFixInChatButton(marker: IMarker, model: ITextModel | null): boolean {
		if (!model) {
			return false;
		}

		// 只为错误和警告显示 "Fix in Chat" 按钮
		return marker.severity === MarkerSeverity.Error || marker.severity === MarkerSeverity.Warning;
	}

	private _renderMarkerStatusbar(context: IEditorHoverRenderContext, markerHover: MarkerHover): IDisposable {
		const disposables = new DisposableStore();
		if (markerHover.marker.severity === MarkerSeverity.Error || markerHover.marker.severity === MarkerSeverity.Warning || markerHover.marker.severity === MarkerSeverity.Info) {
			const markerController = MarkerController.get(this._editor);
			if (markerController) {
				context.statusBar.addAction({
					label: nls.localize('view problem', "查看问题"),
					commandId: NextMarkerAction.ID,
					run: () => {
						context.hide();
						markerController.showAtMarker(markerHover.marker);
						this._editor.focus();
					}
				});
			}
		}

		if (!this._editor.getOption(EditorOption.readOnly)) {
			const quickfixPlaceholderElement = context.statusBar.append($('div'));
			if (this.recentMarkerCodeActionsInfo) {
				if (IMarkerData.makeKey(this.recentMarkerCodeActionsInfo.marker) === IMarkerData.makeKey(markerHover.marker)) {
					if (!this.recentMarkerCodeActionsInfo.hasCodeActions) {
						quickfixPlaceholderElement.textContent = nls.localize('noQuickFixes', "No quick fixes available");
					}
				} else {
					this.recentMarkerCodeActionsInfo = undefined;
				}
			}
			const updatePlaceholderDisposable = this.recentMarkerCodeActionsInfo && !this.recentMarkerCodeActionsInfo.hasCodeActions ? Disposable.None : disposableTimeout(() => quickfixPlaceholderElement.textContent = nls.localize('checkingForQuickFixes', "Checking for quick fixes..."), 200, disposables);
			if (!quickfixPlaceholderElement.textContent) {
				// Have some content in here to avoid flickering
				quickfixPlaceholderElement.textContent = String.fromCharCode(0xA0); // &nbsp;
			}
			const codeActionsPromise = this.getCodeActions(markerHover.marker);
			disposables.add(toDisposable(() => codeActionsPromise.cancel()));
			codeActionsPromise.then(actions => {
				updatePlaceholderDisposable.dispose();
				this.recentMarkerCodeActionsInfo = { marker: markerHover.marker, hasCodeActions: actions.validActions.length > 0 };

				if (!this.recentMarkerCodeActionsInfo.hasCodeActions) {
					actions.dispose();
					quickfixPlaceholderElement.textContent = nls.localize('noQuickFixes', "No quick fixes available");
					return;
				}
				quickfixPlaceholderElement.style.display = 'none';

				let showing = false;
				disposables.add(toDisposable(() => {
					if (!showing) {
						actions.dispose();
					}
				}));

				context.statusBar.addAction({
					label: nls.localize('quick fixes', "快速修复..."),
					commandId: quickFixCommandId,
					run: (target) => {
						showing = true;
						const controller = CodeActionController.get(this._editor);
						const elementPosition = dom.getDomNodePagePosition(target);
						// Hide the hover pre-emptively, otherwise the editor can close the code actions
						// context menu as well when using keyboard navigation
						context.hide();
						controller?.showCodeActions(markerCodeActionTrigger, actions, {
							x: elementPosition.left,
							y: elementPosition.top,
							width: elementPosition.width,
							height: elementPosition.height
						});
					}
				});

				const aiCodeAction = actions.validActions.find(action => action.action.isAI);
				if (aiCodeAction) {
					context.statusBar.addAction({
						label: aiCodeAction.action.title,
						commandId: aiCodeAction.action.command?.id ?? '',
						run: () => {
							const controller = CodeActionController.get(this._editor);
							controller?.applyCodeAction(aiCodeAction, false, false, ApplyCodeActionReason.FromProblemsHover);
						}
					});
				}

			}, onUnexpectedError);
		}
		return disposables;
	}

	private getCodeActions(marker: IMarker): CancelablePromise<CodeActionSet> {
		return createCancelablePromise(cancellationToken => {
			return getCodeActions(
				this._languageFeaturesService.codeActionProvider,
				this._editor.getModel()!,
				new Range(marker.startLineNumber, marker.startColumn, marker.endLineNumber, marker.endColumn),
				markerCodeActionTrigger,
				Progress.None,
				cancellationToken);
		});
	}
}
