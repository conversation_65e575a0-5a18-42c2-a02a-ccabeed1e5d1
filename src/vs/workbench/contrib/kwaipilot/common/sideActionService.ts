/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Event } from '../../../../base/common/event.js';

export const ISideActionService = createDecorator<ISideActionService>('sideActionService');

/**
 * Sidebar UI 挂载信息
 */
export interface ISidebarMountInfo {
	/** 挂载的UI类型 */
	uiType: string;
	/** 是否已挂载 */
	mounted: boolean;
	/** 挂载时间戳 */
	timestamp: number;
	/** 额外的挂载参数 */
	params?: Record<string, any>;
}

/**
 * Sidebar 操作方法的定义
 */
export interface ISidebarAction {
	/** 操作的唯一标识 */
	id: string;
	/** 操作名称 */
	name: string;
	/** 操作描述 */
	description?: string;
	/** 操作的处理函数 */
	handler: (...args: any[]) => any;
	/** 操作的上下文 */
	context?: any;
}

/**
 * Side Action Service 接口
 * 用于管理 sidebar 内的UI挂载和方法注册
 */
export interface ISideActionService {
	readonly _serviceBrand: undefined;

	/**
	 * 当前UI挂载信息，如果未挂载则为 undefined
	 */
	readonly mountedUI: ISidebarMountInfo | undefined;

	/**
	 * 当前已注册的sidebar操作
	 */
	readonly registeredActions: readonly ISidebarAction[];

	/**
	 * UI挂载状态变化事件
	 */
	readonly onDidChangeMountState: Event<ISidebarMountInfo>;

	/**
	 * 操作注册状态变化事件
	 */
	readonly onDidChangeActions: Event<ISidebarAction>;

	/**
	 * 挂载UI到sidebar
	 * @param mountInfo UI挂载信息
	 * @returns 是否挂载成功
	 */
	mountUI(mountInfo: Omit<ISidebarMountInfo, 'mounted' | 'timestamp'>): boolean;

	/**
	 * 卸载UI
	 * @returns 是否卸载成功
	 */
	unmountUI(): boolean;

	/**
	 * 检查UI是否已挂载
	 * @returns 是否已挂载
	 */
	isUIMounted(): boolean;

	/**
	 * 注册sidebar操作方法
	 * @param action 操作定义
	 * @returns 是否注册成功
	 */
	registerAction(action: ISidebarAction): boolean;

	/**
	 * 注销sidebar操作方法
	 * @param actionId 操作ID
	 * @returns 是否注销成功
	 */
	unregisterAction(actionId: string): boolean;

	/**
	 * 执行已注册的操作
	 * @param actionId 操作ID
	 * @param args 操作参数
	 * @returns 操作执行结果
	 */
	executeAction(actionId: string, ...args: any[]): Promise<any>;

	/**
	 * 获取已注册的操作
	 * @param actionId 操作ID
	 * @returns 操作定义，如果未找到则返回undefined
	 */
	getAction(actionId: string): ISidebarAction | undefined;

	/**
	 * 检查操作是否已注册
	 * @param actionId 操作ID
	 * @returns 是否已注册
	 */
	hasAction(actionId: string): boolean;

	/**
	 * 清空所有注册的操作
	 */
	clearAllActions(): void;

	/**
	 * 获取服务状态信息
	 * @returns 包含挂载UI和注册操作数量的状态信息
	 */
	getServiceStatus(): {
		isUIMounted: boolean;
		registeredActionCount: number;
		mountedUI: ISidebarMountInfo | undefined;
		registeredActions: readonly ISidebarAction[];
	};
}
