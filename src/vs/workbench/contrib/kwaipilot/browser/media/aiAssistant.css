.ai-assistant-container {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.ai-assistant-container .no_login.no_login.main-frame {
	box-sizing: border-box;
	padding: 11px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	align-self: stretch;
	gap: 16px;
}

.ai-assistant-container .no_login.main-frame .sub-frame {
	box-sizing: border-box;
	height: 116px;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24px;
}

.ai-assistant-container .no_login.main-frame .sub-frame .inner-frame {
	box-sizing: border-box;
	height: 56px;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 2px;
}

.ai-assistant-container
	.no_login.main-frame
	.sub-frame
	.inner-frame
	.pilot-section {
	box-sizing: border-box;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 22px;
	min-width: 297px;
	line-height: 33px;
}

.ai-assistant-container
	.no_login.main-frame
	.sub-frame
	.inner-frame
	.pilot-section
	.pilot-feature-primary {
	color: var(--vscode-foreground);
}

.ai-assistant-container
	.no_login.main-frame
	.sub-frame
	.inner-frame
	.pilot-section
	.pilot-feature-secondary {
	color: var(--vscode-textLink-foreground);
}

.ai-assistant-container
	.no_login.main-frame
	.sub-frame
	.inner-frame
	.speed-section {
	margin-top: 2px;
	box-sizing: border-box;
	width: 177px;
	display: flex;
	justify-content: center;
	align-items: center;

	color: var(--vscode-descriptionForeground);
	font-size: 14px;
	min-width: 177px;
	text-align: center;
	vertical-align: middle;
	line-height: 21px;
	font-family: "PingFang SC";
}

.ai-assistant-container .no_login.main-frame .sub-frame .action-button {
	box-sizing: border-box;
	width: 240px;
	height: 36px;
	padding: 6px 64px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: var(--vscode-button-background);
	color: var(--vscode-button-secondaryForeground);
	overflow: hidden;
	border-radius: 4px;
	gap: 10px;
}

.ai-assistant-container .no_login.main-frame .sub-frame .action-button:hover {
	background: var(--vscode-button-hoverBackground);
	cursor: pointer;
}

.ai-assistant-container
	.no_login.main-frame
	.sub-frame
	.action-button
	.button-label {
	box-sizing: border-box;
	height: 17px;
	display: flex;
	align-items: center;
	color: var(--vscode-button-foreground);
	font-size: 14px;
	min-width: 41px;
	vertical-align: middle;
	line-height: 16.71px;
	font-family: "SF Pro Text";
	font-weight: 500;
}



#workbench\.parts\.auxiliarybar > div.composite.title {
	height: 0;
}


/* WARN:隐藏掉横向滚动条，否则看起来下面会空白一块 */
#workbench\.view\.aiAssistant > div > div > div.monaco-scrollable-element.mac > div.split-view-container > div > div > div.pane-body > div > div > div > div > div.w-full.h-screen.flex.flex-col > div > div{
	overflow-x: auto;
}

/* 暂时补齐 antd 的样式，后面再debug */
.ai-assistant-container .ant-typography-ellipsis {
    display: inline-block;
    max-width: 100%;
}
