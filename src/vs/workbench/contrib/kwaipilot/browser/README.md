# SideActionService

一个用于管理 sidebar 内 UI 挂载和方法注册的服务，提供给外部调用。

## 功能特性

### 🎨 UI 挂载管理
- **mountUI()** - 挂载 UI 组件到 sidebar
- **unmountUI()** - 卸载 UI 组件
- **isUIMounted()** - 检查 UI 是否已挂载

### ⚡ 操作方法注册
- **registerAction()** - 注册 sidebar 操作方法
- **unregisterAction()** - 注销指定操作方法
- **executeAction()** - 执行已注册的操作
- **getAction()** - 获取操作定义
- **hasAction()** - 检查操作是否已注册
- **clearAllActions()** - 清空所有注册的操作

### 📡 事件监听
- **onDidChangeMountState** - UI 挂载状态变化事件
- **onDidChangeActions** - 操作注册状态变化事件

### 📊 状态管理
- **getServiceStatus()** - 获取完整的服务状态信息

## 快速开始

### 1. 依赖注入使用

```typescript
import { ISideActionService } from '../common/sideActionService.js';

export class MyComponent {
	constructor(
		// 通过依赖注入获取服务
		@ISideActionService private readonly sideActionService: ISideActionService
	) {
		this.initialize();
	}
}
```

### 2. 挂载 UI

```typescript
// 挂载聊天界面
const mounted = this.sideActionService.mountUI({
	uiType: 'chat-interface',
	params: {
		theme: 'dark',
		width: 400
	}
});

if (mounted) {
	console.log('UI 挂载成功');
}
```

### 3. 注册操作

```typescript
// 注册发送消息操作
const action = {
	id: 'kwaipilot.sendMessage',
	name: '发送消息',
	description: '向聊天界面发送消息',
	handler: async (message: string) => {
		// 处理发送消息的逻辑
		return { success: true, messageId: Date.now() };
	}
};

this.sideActionService.registerAction(action);
```

### 4. 执行操作

```typescript
// 执行已注册的操作
try {
	const result = await this.sideActionService.executeAction(
		'kwaipilot.sendMessage',
		'Hello World!'
	);
	console.log('操作执行结果:', result);
} catch (error) {
	console.error('操作执行失败:', error);
}
```

### 5. 监听事件

```typescript
// 监听 UI 挂载状态变化
this.sideActionService.onDidChangeMountState((mountInfo) => {
	if (mountInfo.mounted) {
		console.log(`UI ${mountInfo.uiType} 已挂载`);
	} else {
		console.log(`UI ${mountInfo.uiType} 已卸载`);
	}
});

// 监听操作注册变化
this.sideActionService.onDidChangeActions((action) => {
	console.log(`操作 ${action.id} 状态已变化`);
});
```

## API 参考

### 类型定义

#### ISidebarMountInfo
```typescript
interface ISidebarMountInfo {
	/** 挂载的UI类型 */
	uiType: string;
	/** 是否已挂载 */
	mounted: boolean;
	/** 挂载时间戳 */
	timestamp: number;
	/** 额外的挂载参数 */
	params?: Record<string, any>;
}
```

#### ISidebarAction
```typescript
interface ISidebarAction {
	/** 操作的唯一标识 */
	id: string;
	/** 操作名称 */
	name: string;
	/** 操作描述 */
	description?: string;
	/** 操作的处理函数 */
	handler: (...args: any[]) => any;
	/** 操作的上下文 */
	context?: any;
}
```

### 主要方法

#### mountUI(mountInfo)
挂载 UI 到 sidebar
- **参数**: `Omit<ISidebarMountInfo, 'mounted' | 'timestamp'>`
- **返回**: `boolean` - 是否挂载成功
- **注意**: 同时只能挂载一个UI，如果已有UI挂载则返回false

#### unmountUI()
卸载当前的 UI
- **返回**: `boolean` - 是否卸载成功

#### isUIMounted()
检查UI是否已挂载
- **返回**: `boolean` - 是否已挂载

#### registerAction(action)
注册 sidebar 操作方法
- **参数**: `ISidebarAction` - 操作定义
- **返回**: `boolean` - 是否注册成功

#### executeAction(actionId, ...args)
执行已注册的操作
- **参数**: `string, ...any[]` - 操作ID和参数
- **返回**: `Promise<any>` - 操作执行结果

#### getServiceStatus()
获取服务状态信息
- **返回**: 包含挂载UI和注册操作的状态对象

## 使用场景

### 1. 聊天界面管理
```typescript
// 挂载聊天UI
this.sideActionService.mountUI({
	uiType: 'chat',
	params: { theme: 'dark' }
});

// 注册聊天操作
this.sideActionService.registerAction({
	id: 'chat.send',
	name: '发送消息',
	handler: (message) => this.handleSendMessage(message)
});
```

### 2. 设置面板管理
```typescript
// 挂载设置面板
this.sideActionService.mountUI({
	uiType: 'settings',
	params: { collapsible: true }
});

// 注册设置操作
this.sideActionService.registerAction({
	id: 'settings.save',
	name: '保存设置',
	handler: (config) => this.handleSaveConfig(config)
});
```

## 最佳实践

### 1. 错误处理
```typescript
try {
	const result = await this.sideActionService.executeAction('myAction', param);
	// 处理成功结果
} catch (error) {
	// 处理错误
	console.error('Action execution failed:', error);
}
```

### 2. 资源清理
```typescript
class MyComponent {
	dispose() {
		// 清理挂载的UI
		this.sideActionService.unmountUI();

		// 清理注册的操作
		this.sideActionService.unregisterAction('my-action');
	}
}
```

### 3. 状态监听
```typescript
// 监听状态变化并做出响应
this.sideActionService.onDidChangeMountState((mountInfo) => {
	this.updateUIState(mountInfo);
});
```

### 4. 批量操作
```typescript
// 注册多个相关操作
const actions = [
	{ id: 'action1', name: 'Action 1', handler: this.handler1 },
	{ id: 'action2', name: 'Action 2', handler: this.handler2 },
	{ id: 'action3', name: 'Action 3', handler: this.handler3 }
];

actions.forEach(action => {
	this.sideActionService.registerAction(action);
});
```

## 注意事项

1. **单UI限制**: 同时只能挂载一个UI，挂载新UI前需要先卸载当前UI
2. **唯一性**: 确保 `actionId` 的唯一性
3. **内存管理**: 及时清理不再使用的操作
4. **错误处理**: 始终处理可能的异常情况
5. **事件监听**: 合理使用事件监听来响应状态变化
6. **参数验证**: 在操作处理函数中验证输入参数

## 扩展示例

查看 `sideActionService.example.ts` 文件获取更多详细的使用示例和最佳实践。
