/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { ISideActionService, ISidebarMountInfo, ISidebarAction } from '../common/sideActionService.js';
import { ILogService } from '../../../../platform/log/common/log.js';

/**
 * Side Action Service 实现类
 * 负责管理 sidebar 内的UI挂载和方法注册
 */
export class SideActionService extends Disposable implements ISideActionService {

	readonly _serviceBrand: undefined;

	// 内部状态存储
	private _mountedUI: ISidebarMountInfo | undefined = undefined;
	private readonly _registeredActions = new Map<string, ISidebarAction>();

	// 事件发射器
	private readonly _onDidChangeMountState = this._register(new Emitter<ISidebarMountInfo>());
	private readonly _onDidChangeActions = this._register(new Emitter<ISidebarAction>());

	constructor(
		@ILogService private readonly logService: ILogService
	) {
		super();
		this.logService.info('[SideActionService] Service initialized');
	}

	// 公开的只读属性
	get mountedUI(): ISidebarMountInfo | undefined {
		return this._mountedUI;
	}

	get registeredActions(): readonly ISidebarAction[] {
		return Array.from(this._registeredActions.values());
	}

	get onDidChangeMountState(): Event<ISidebarMountInfo> {
		return this._onDidChangeMountState.event;
	}

	get onDidChangeActions(): Event<ISidebarAction> {
		return this._onDidChangeActions.event;
	}

	/**
	 * 挂载UI到sidebar
	 * @param mountInfo UI挂载信息
	 * @returns 是否挂载成功
	 */
	mountUI(mountInfo: Omit<ISidebarMountInfo, 'mounted' | 'timestamp'>): boolean {
		try {
			// 检查是否已经挂载
			if (this._mountedUI) {
				this.logService.warn(`[SideActionService] UI is already mounted: ${this._mountedUI.uiType}`);
				return false;
			}

			// 创建完整的挂载信息
			const fullMountInfo: ISidebarMountInfo = {
				...mountInfo,
				mounted: true,
				timestamp: Date.now()
			};

			// 存储挂载信息
			this._mountedUI = fullMountInfo;

			// 触发事件
			this._onDidChangeMountState.fire(fullMountInfo);

			this.logService.info(`[SideActionService] UI mounted successfully: ${mountInfo.uiType}`);
			return true;

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to mount UI: ${error}`);
			return false;
		}
	}

	/**
	 * 卸载UI
	 * @returns 是否卸载成功
	 */
	unmountUI(): boolean {
		try {
			if (!this._mountedUI) {
				this.logService.warn(`[SideActionService] No UI is currently mounted`);
				return false;
			}

			// 创建卸载事件信息
			const unmountInfo: ISidebarMountInfo = {
				...this._mountedUI,
				mounted: false,
				timestamp: Date.now()
			};

			// 清除挂载信息
			this._mountedUI = undefined;

			// 触发事件
			this._onDidChangeMountState.fire(unmountInfo);

			this.logService.info(`[SideActionService] UI unmounted successfully: ${unmountInfo.uiType}`);
			return true;

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to unmount UI: ${error}`);
			return false;
		}
	}

	/**
	 * 检查UI是否已挂载
	 * @returns 是否已挂载
	 */
	isUIMounted(): boolean {
		return this._mountedUI !== undefined;
	}

	/**
	 * 注册sidebar操作方法
	 * @param action 操作定义
	 * @returns 是否注册成功
	 */
	registerAction(action: ISidebarAction): boolean {
		try {
			const { id } = action;

			// 检查是否已经注册
			if (this._registeredActions.has(id)) {
				this.logService.warn(`[SideActionService] Action with id '${id}' is already registered`);
				return false;
			}

			// 验证操作定义的完整性
			if (!action.name || typeof action.handler !== 'function') {
				this.logService.error(`[SideActionService] Invalid action definition for id '${id}'`);
				return false;
			}

			// 存储操作
			this._registeredActions.set(id, action);

			// 触发事件
			this._onDidChangeActions.fire(action);

			this.logService.info(`[SideActionService] Action registered successfully: ${id} (${action.name})`);
			return true;

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to register action: ${error}`);
			return false;
		}
	}

	/**
	 * 注销sidebar操作方法
	 * @param actionId 操作ID
	 * @returns 是否注销成功
	 */
	unregisterAction(actionId: string): boolean {
		try {
			const action = this._registeredActions.get(actionId);

			if (!action) {
				this.logService.warn(`[SideActionService] Action with id '${actionId}' is not registered`);
				return false;
			}

			// 删除操作
			this._registeredActions.delete(actionId);

			// 触发事件
			this._onDidChangeActions.fire(action);

			this.logService.info(`[SideActionService] Action unregistered successfully: ${actionId}`);
			return true;

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to unregister action: ${error}`);
			return false;
		}
	}

	/**
	 * 执行已注册的操作
	 * @param actionId 操作ID
	 * @param args 操作参数
	 * @returns 操作执行结果
	 */
	async executeAction(actionId: string, ...args: any[]): Promise<any> {
		try {
			const action = this._registeredActions.get(actionId);

			if (!action) {
				throw new Error(`Action with id '${actionId}' is not registered`);
			}

			this.logService.info(`[SideActionService] Executing action: ${actionId} with args:`, args);

			// 执行操作处理函数
			const result = await action.handler.apply(action.context, args);

			this.logService.info(`[SideActionService] Action executed successfully: ${actionId}`);
			return result;

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to execute action '${actionId}': ${error}`);
			throw error;
		}
	}

	/**
	 * 获取已注册的操作
	 * @param actionId 操作ID
	 * @returns 操作定义，如果未找到则返回undefined
	 */
	getAction(actionId: string): ISidebarAction | undefined {
		return this._registeredActions.get(actionId);
	}

	/**
	 * 检查操作是否已注册
	 * @param actionId 操作ID
	 * @returns 是否已注册
	 */
	hasAction(actionId: string): boolean {
		return this._registeredActions.has(actionId);
	}

	/**
	 * 清空所有注册的操作
	 */
	clearAllActions(): void {
		try {
			const actionCount = this._registeredActions.size;

			// 清空映射
			this._registeredActions.clear();

			this.logService.info(`[SideActionService] Cleared all registered actions (${actionCount} actions)`);

		} catch (error) {
			this.logService.error(`[SideActionService] Failed to clear registered actions: ${error}`);
		}
	}

	/**
	 * 获取服务状态信息
	 * @returns 包含挂载UI和注册操作数量的状态信息
	 */
	getServiceStatus(): {
		isUIMounted: boolean;
		registeredActionCount: number;
		mountedUI: ISidebarMountInfo | undefined;
		registeredActions: readonly ISidebarAction[];
	} {
		return {
			isUIMounted: this._mountedUI !== undefined,
			registeredActionCount: this._registeredActions.size,
			mountedUI: this._mountedUI,
			registeredActions: this.registeredActions
		};
	}

	/**
	 * 清理资源
	 */
	override dispose(): void {
		// 卸载UI
		if (this._mountedUI) {
			this.unmountUI();
		}

		// 清空所有操作
		this.clearAllActions();

		// 调用父类的清理方法
		super.dispose();

		this.logService.info('[SideActionService] Service disposed');
	}
}
