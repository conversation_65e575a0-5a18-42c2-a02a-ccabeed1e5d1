import { ICommandService, CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { VSBuffer } from '../../../../base/common/buffer.js';
import { joinPath } from '../../../../base/common/resources.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import {
	IBridgeService,
	BridgeMessage,
	CallbackFunction,
	HandlerFunction,
	MessageListener
} from '../common/bridgeService.js';

/**
 * Bridge 服务实现
 * 提供 WebView 和 Extension 之间的基础桥接通信功能
 * 使用单例模式管理全局桥接状态
 */
export class BridgeService extends Disposable implements IBridgeService {
	readonly _serviceBrand: undefined;

	private callbacks: Map<number, CallbackFunction>;
	private handlers: Map<string, HandlerFunction>;
	private callbackId: number;
	private messageListeners: Set<MessageListener>;

	constructor(
		@ICommandService private readonly commandService: ICommandService,
		@IFileService private readonly fileService: IFileService,
		@IEnvironmentService private readonly environmentService: IEnvironmentService
	) {
		super();

		this.callbacks = new Map();
		this.handlers = new Map();
		this.callbackId = 0;
		this.messageListeners = new Set();

		// 注册桥接命令
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.postMessageFromExtension`,
			handler: async (accessor, message: BridgeMessage) => {
				if (message.protocol === 'callback') {
					const callback = this.callbacks.get(message.callbackId!);
					if (callback) {
						callback(message.data);
						this.callbacks.delete(message.callbackId!);
					}
				}
				else if (message.protocol === 'callHandler') {
					const handler = this.handlers.get(message.name!);
					if (handler) {
						const response = await handler(message.data);
						this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
							protocol: 'callback',
							callbackId: message.callbackId,
							data: response,
						});
					}
				}
				else if (message.protocol === 'message') {
					this.messageListeners.forEach((listener) => {
						listener(message);
					});
				}
			}
		}));
	}

	callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
		const callbackId = this.callbackId++;
		if (callback) {
			this.callbacks.set(callbackId, callback);
		}
		this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
			protocol: 'callHandler',
			name: handlerName,
			callbackId,
			data,
		});
	}

	registerHandler(name: string, handler: HandlerFunction): void {
		this.handlers.set(name, handler);
	}

	reigisterBridgeCommand(commandName: string, onHandler: HandlerFunction): void {
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.${commandName}`,
			handler: (accessor, message: BridgeMessage) => {
				return onHandler(message);
			}
		}));
	}

	/**
	 * 保存 ArrayBuffer 到临时文件
	 * @param arrayBuffer 要保存的 ArrayBuffer 数据
	 * @param fileName 原始文件名（可选，用于提取扩展名）
	 * @returns 临时文件路径
	 */
	private async saveArrayBufferToTempFile(arrayBuffer: ArrayBuffer, fileName?: string): Promise<string> {
		// 创建临时目录
		const tempDirName = 'kwaipilot-images';
		const tempDirUri = joinPath(this.environmentService.cacheHome, tempDirName);

		// 确保临时目录存在
		const tempDirExists = await this.fileService.exists(tempDirUri);
		if (!tempDirExists) {
			await this.fileService.createFolder(tempDirUri);
		}

		// 生成唯一文件名
		const ext = fileName ? fileName.split('.').pop() : 'bin';
		const uniqueFileName = `${generateUuid()}.${ext}`;
		const tempFileUri = joinPath(tempDirUri, uniqueFileName);

		// 将 ArrayBuffer 转换为 VSBuffer 并写入文件
		const buffer = VSBuffer.wrap(new Uint8Array(arrayBuffer));
		await this.fileService.writeFile(tempFileUri, buffer);

		return tempFileUri.fsPath;
	}

	private async $uploadImageDirectly(list: { data: ArrayBuffer | Uint8Array; name?: string; fileName?: string }[]) {
		for (const item of list) {
			// 检查是否有需要保存的数据
			if (item.data && (item.data instanceof ArrayBuffer || item.data instanceof Uint8Array)) {
				// 存入临时文件，把路径覆盖 data 属性
				try {
					const tempFilePath = await this.saveArrayBufferToTempFile(
						item.data instanceof ArrayBuffer ? item.data : item.data.buffer,
						item.name || item.fileName || 'upload.bin'
					);

					// 用文件路径替换 data 属性
					// @ts-ignore
					item.uri = tempFilePath;
				} catch (error) {
					console.error('Failed to save ArrayBuffer to temporary file:', error);
					// 如果保存失败，保持原始数据
				}
			}
		}
	}

	async postMessage(message: any): Promise<void> {
		// @IMP: 特殊处理 arraybuffer 的情况 后面再改造插件里的 通信把，目前的通信不支持 非 string 的方式
		if (message?.payload?.method === '$uploadImageDirectly') {
			const list = message?.payload?.args?.[0];
			this.$uploadImageDirectly(list);
		}
		this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
			protocol: 'message',
			data: message,
		});
	}

	addMessageListener(listener: MessageListener): void {
		this.messageListeners.add(listener);
	}

	removeMessageListener(listener: MessageListener): void {
		this.messageListeners.delete(listener);
	}

	/**
	 * 清理资源
	 */
	override dispose(): void {
		this.callbacks.clear();
		this.handlers.clear();
		this.messageListeners.clear();
		super.dispose();
	}
}

// 注册为单例服务
registerSingleton(IBridgeService, BridgeService, InstantiationType.Delayed);
