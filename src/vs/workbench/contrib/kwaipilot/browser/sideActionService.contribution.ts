/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { ISideActionService } from '../common/sideActionService.js';
import { SideActionService } from './sideActionService.js';

/**
 * 注册 SideActionService 为单例服务
 *
 * 这将使得 SideActionService 在整个 workbench 中可通过依赖注入使用
 * 服务实例将在首次使用时创建，并在整个应用生命周期中保持唯一
 */
registerSingleton(
	ISideActionService,
	SideActionService,
	InstantiationType.Delayed // 延迟实例化，只在首次使用时创建
);
