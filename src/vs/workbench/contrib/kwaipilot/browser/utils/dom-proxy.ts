import { isHTMLElement } from '../../../../../base/browser/dom.js';

/**
 * 拦截 html 元素 style 里的 color-scheme 属性设置
 * @param fromPath 来源路径
 * @param win 当前窗口对象
 */
export function interceptHTMLSetting(fromPath: string, win: Window) {
	const htmlElement = win.document.documentElement;

	// 检查是否已经设置了代理，避免重复设置
	// @ts-ignore
	if (htmlElement._kwaipilotStyleProxyApplied) {
		console.log('Style proxy already applied, skipping');
		return;
	}

	const originalStyle = htmlElement.style;

	// 定义高亮样式
	const highlightStyle = 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;';

	/**
	 * 检查设置操作是否来自指定路径并处理拦截
	 * @param prop 属性名
	 * @returns 是否拦截设置操作
	 */
	function checkAndBlock(prop: PropertyKey) {
		// allow-any-unicode-next-line
		const error = new Error('尝试设置属性');
		const isFromKwaipilotWebviewUI = error.stack?.includes(fromPath);
		if (isFromKwaipilotWebviewUI) {
			// allow-any-unicode-next-line
			console.log(`%c阻止来自插件的设置 ${String(prop)} 属性，代码路径：%o`, highlightStyle, error.stack);
			return true;
		}
		// @TODO: 生产环境的路径判断不可靠，目前暂时只能全部拦截
		if (prop === 'colorScheme') {
			// allow-any-unicode-next-line
			console.log(`%c阻止设置 colorScheme 属性，代码路径：%o`, highlightStyle, error.stack);
			return true;
		}
		return false;
	}

	// 创建 style 对象的代理
	const styleProxy = new Proxy(originalStyle, {
		set(target, prop, value) {
			if (checkAndBlock(prop)) {
				return true;
			}
			return Reflect.set(target, prop, value);
		},
		defineProperty(target, prop, attributes) {
			if (checkAndBlock(prop)) {
				return true;
			}
			return Reflect.defineProperty(target, prop, attributes);
		}
	});

	try {
		// 将代理的 style 对象赋值给 html 元素
		Object.defineProperty(htmlElement, 'style', {
			get: () => styleProxy,
			set: (newStyle) => {
				for (const prop in newStyle) {
					if (checkAndBlock(prop)) {
						continue;
					}
					// @ts-ignore
					originalStyle[prop] = newStyle[prop];
				}
			},
			configurable: true // 允许重新配置，避免重复定义错误
		});

		// 标记已经应用了代理
		// @ts-ignore
		htmlElement._kwaipilotStyleProxyApplied = true;
	} catch (error) {
		console.warn('Failed to apply style proxy:', error);
	}
}

/**
 * 拦截 html 元素的 dataset 属性设置 data-theme
 * @param fromPath
 * @param win
 * @param target
 */
export function interceptHTMLDataset(fromPath: string, win: Window, targetHtml: HTMLDivElement) {
	const htmlElement = win.document.documentElement;
	// 检查是否已经设置了代理，避免重复设置
	// @ts-ignore
	if (htmlElement._kwaipilotDatasetProxyApplied) {
		console.log('Dataset proxy already applied, skipping');
		return;
	}
	const originalDataset = htmlElement.dataset;
	// 定义高亮样式
	const highlightStyle = 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;';
	/**
	 * 检查设置操作是否来自指定路径并处理拦截
	 * @param prop 属性名
	 * @returns 是否拦截设置操作
	 */
	function checkAndBlock(prop: PropertyKey) {
		// allow-any-unicode-next-line
		const error = new Error('尝试设置属性');
		const isFromKwaipilotWebviewUI = error.stack?.includes(fromPath);
		if (isFromKwaipilotWebviewUI) {
			// allow-any-unicode-next-line
			console.log(`%c阻止来自插件的设置 ${String(prop)} 属性，代码路径：%o`, highlightStyle, error.stack);
			return true;
		}
		if (prop === 'theme') {
			// allow-any-unicode-next-line
			console.log(`%c阻止设置 theme 属性，代码路径：%o`, highlightStyle, error.stack);
			return true;
		}
		return false;
	}

	// 创建 dataset 对象的代理
	const datasetProxy = new Proxy(originalDataset, {
		set(target, prop, value) {
			if (checkAndBlock(prop)) {
				if (typeof prop === 'string') {
					targetHtml.dataset[prop] = value; // 确保设置值
				}
				return true;
			}
			return Reflect.set(target, prop, value);
		},
		defineProperty(target, property, attributes) {
			if (checkAndBlock(property)) {
				// return Reflect.defineProperty(targetHtml, property, attributes);
				if (typeof property === 'string') {
					targetHtml.dataset[property] = attributes.value; // 确保设置值
				}
				return true;
			}
			return Reflect.defineProperty(target, property, attributes);
		},
	});

	try {
		// 将代理的 dataset 对象赋值给 html 元素
		Object.defineProperty(htmlElement, 'dataset', {
			get: () => datasetProxy,
			set: (newDataset) => {
				for (const prop in newDataset) {
					if (checkAndBlock(prop)) {
						targetHtml.dataset[prop] = newDataset[prop];
						continue;
					}
					// @ts-ignore
					originalDataset[prop] = newDataset[prop];
				}
			},
			configurable: true // 允许重新配置，避免重复定义错误
		});

		// 标记已经应用了代理
		// @ts-ignore
		htmlElement._kwaipilotDatasetProxyApplied = true;
	}
	catch (error) {
		console.warn('Failed to apply dataset proxy:', error);
	}

}


/**
 * 拦截 三方库 比如 emotion 的 style 表的插入 style[data-emotion]
 * @param fromPath
 * @param win
 */
export function interceptStyleSheet(fromPath: string, win: Window) {
	const originalAppendChild = win.document.head.appendChild;
	const originalInsertBefore = win.document.head.insertBefore;

	const checkAndBlock = (node: Node) => {
		// allow-any-unicode-next-line
		const error = new Error('尝试插入 style 表');
		const isFromKwaipilotWebviewUI = error.stack?.includes(fromPath);
		if (isFromKwaipilotWebviewUI) {
			// allow-any-unicode-next-line
			console.log(`%c阻止来自插件的插入 style 表，代码路径：%o`, 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;', error.stack);
			return true;
		}

		if (isHTMLElement(node) && node.dataset.emotion) {
			// allow-any-unicode-next-line
			console.log(`%c阻止插入 style 表，代码路径：%o`, 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;', error.stack);
			return true;
		}
		return false;
	};
	// @ts-ignore
	win.document.head.appendChild = function (node) {
		if (checkAndBlock(node)) {
			// allow-any-unicode-next-line
			console.log('阻止插入 style 表', node);
			//  empty style
			// node.textContent = 'fffff{}'; // 占位符，保证 get css rule 不报错

			// // 需要拦截 node.insertRule 方法
			// if (isHTMLElement(node) && node.tagName === 'STYLE') {
			// 	let styleNode = node as HTMLStyleElement;
			// 	const originalInsertRule = styleNode.sheet?.insertRule;
			// 	if (styleNode.sheet) {
			// 		styleNode.sheet.insertRule = function (rule, index) {

			// 		}
			// 	}

			// }

			return;
		}
		return originalAppendChild.call(win.document.head, node);
	};

	// 还有其他的插入方式 insertBefore
	// @ts-ignore
	win.document.head.insertBefore = function (node, refNode) {
		if (checkAndBlock(node)) {
			// allow-any-unicode-next-line
			console.log('阻止插入 style 表', node);
			//  empty style
			// node.textContent = 'fffff{}'; // 占位符，保证 get css rule 不报错

			return;
		}
		return originalInsertBefore.call(win.document.head, node, refNode);
	};


}

const cssClassMap = {
	html: '[data-element-type="html"]',
	body: '[data-element-type="body"]',
	':root': '[data-element-type="html"]',
};

function replaceSelector(selector: string) {
	// 拆分 selector 为多个部分
	const parts = selector.split(' ');

	// 遍历每个部分，进行替换
	const replacedParts = parts.map((part) => {
		// 检查是否需要替换
		for (const [key, value] of Object.entries(cssClassMap)) {
			if (part === key) {
				return value; // 替换为对应的值
			}
		}
		// 如果没有匹配到，保持原样
		return part;
	});

	// 重新组合为完整的 selector
	return replacedParts.join(' ');
}

/**
 * 拦截 css 规则的插入 @IMP: 这个方法有问题，会导致样式表被覆盖  谨慎修改 这里只是为了拦截 插件的 style 表
 * @param fromPath
 * @param cssPrefix
 */
export function overrideCssRule(fromPath: string, cssPrefix: string) {
	const originalInsertRule = CSSStyleSheet.prototype.insertRule;
	CSSStyleSheet.prototype.insertRule = function (rule, index) {
		const checkAndBlock = (node: Node) => {
			// allow-any-unicode-next-line
			const error = new Error('尝试插入 style 表');
			const isFromKwaipilotWebviewUI = error.stack?.includes(fromPath);
			if (isFromKwaipilotWebviewUI) {
				// allow-any-unicode-next-line
				console.log(`%c阻止来自插件的插入 style 表，代码路径：%o`, 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;', error.stack);
				return true;
			}
			// 如果是 prod 环境的话，由于 frompath 不可靠，这里就只拦截 data-emotion 的 style 表（如果后续发现有其他的，可以继续加）
			if (isHTMLElement(node) && node.dataset.emotion) {
				// allow-any-unicode-next-line
				console.log(`%c阻止插入 style 表，代码路径：%o`, 'color: white; background-color: red; padding: 2px 4px; border-radius: 3px; font-weight: bold;', error.stack);
				return true;
			}
			return false;
		};

		if (this.ownerNode) {
			const isPlugin = checkAndBlock(this.ownerNode);
			if (isPlugin) {// 在这里添加自定义逻辑
				// 需要跳过 @xxx 的规则
				if (!rule.startsWith('@')) {
					const { selectors, declaration } = parseCSSRule(rule);
					// 给所有的 selector 添加一个类名
					const newSelectors = selectors.map(selector => {
						// 需要 按 cssClassMap 替换 selector 中的 类名
						return `${cssPrefix} ${replaceSelector(selector)}`;
					});
					const newRule = `${newSelectors.join(', ')} { ${declaration} }`;
					return originalInsertRule.call(this, newRule, index);
				}
			}
		}

		// 调用原始的 insertRule 方法
		return originalInsertRule.call(this, rule, index);
	};

}

function parseCSSRule(rule: string) {
	// 使用正则表达式解析规则
	const regex = /([^{]+)\{([^}]*)\}/;
	const match = rule.match(regex);

	if (match) {
		// 提取 selector 部分并去除多余的空格
		const selectorText = match[1].trim();
		// 将选择器按逗号分隔并去除多余的空格
		const selectors = selectorText.split(',').map(s => s.trim());
		// 提取 declaration 部分
		const declaration = match[2].trim();

		return {
			selectors: selectors, // 返回选择器数组
			declaration: declaration // 返回声明部分
		};
	} else {
		throw new Error('Invalid CSS rule');
	}
}


export function interceptFetch(fromPath: string, win: Window) {
	// fetch  拦截 ，强制把  他的 参数 变为
	// fetch(input: RequestInfo | URL, init?: RequestInit) ->  fetch(req:Request)

	// 检查是否已经设置了代理，避免重复设置
	// @ts-ignore
	if (win.fetch._kwaipilotFetchProxyApplied) {
		console.log('Fetch proxy already applied, skipping');
		return;
	}

	// 保存原始的 fetch 方法
	const originalFetch = win.fetch;
	// 定义高亮样式，用于日志输出
	const highlightStyle = 'color: white; background-color: blue; padding: 2px 4px; border-radius: 3px; font-weight: bold;';

	// 创建 fetch 方法的代理
	const fetchProxy = function (...args: any[]) {
		// 记录调用来源 // allow-any-unicode-next-line
		const error = new Error('Fetch 调用');
		const isFromKwaipilotWebviewUI = error.stack?.includes(fromPath);

		// 标准化参数为 Request 对象
		let request: Request;

		if (args[0] instanceof Request) {
			// 如果已经是 Request 对象，直接使用
			request = args[0];
		} else {
			// 否则，根据参数创建新的 Request 对象
			const input: RequestInfo | URL = args[0];
			const init: RequestInit | undefined = args[1];
			request = new Request(input, init);
		}

		if (isFromKwaipilotWebviewUI) {
			// allow-any-unicode-next-line
			console.log(`%c拦截来自插件的 fetch 调用，转换为 Request 对象: %o`, highlightStyle, {
				url: request.url,
				method: request.method,
				stack: error.stack
			});
		}

		// 调用原始的 fetch 方法，但只传递 Request 对象
		return originalFetch.call(win, request);
	};

	// 应用代理
	try {
		win.fetch = fetchProxy;

		// 标记已经应用了代理
		// @ts-ignore
		win.fetch._kwaipilotFetchProxyApplied = true;

		console.log('Successfully applied fetch proxy');
	} catch (error) {
		console.warn('Failed to apply fetch proxy:', error);
	}
}
