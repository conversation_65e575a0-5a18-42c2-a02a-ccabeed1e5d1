/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ISideActionService, ISidebarAction, ISidebarMountInfo } from '../common/sideActionService.js';

/**
 * SideActionService 使用示例
 *
 * 本文件展示了如何在其他组件中使用 SideActionService 的各种功能
 */
export class SideActionServiceExample {

	constructor(
		// 通过依赖注入获取 SideActionService 实例
		private readonly sideActionService: ISideActionService
	) {
		this.setupExamples();
	}

	/**
	 * 设置使用示例
	 */
	private setupExamples(): void {
		this.setupMountUIExample();
		this.setupActionExamples();
		this.setupEventListeners();
	}

	/**
	 * UI挂载功能示例
	 */
	private setupMountUIExample(): void {
		// 挂载聊天界面
		const chatMountInfo = {
			uiType: 'chat-interface',
			params: {
				theme: 'dark',
				position: 'right',
				width: 400
			}
		};

		const isChatMounted = this.sideActionService.mountUI(chatMountInfo);
		console.log('Chat UI mounted:', isChatMounted);

		// 检查UI挂载状态
		const isMounted = this.sideActionService.isUIMounted();
		const mountedUI = this.sideActionService.mountedUI;
		console.log('UI status:', { mounted: isMounted, info: mountedUI });
	}

	/**
	 * 操作注册功能示例
	 */
	private setupActionExamples(): void {
		// 示例1: 注册发送消息操作
		const sendMessageAction: ISidebarAction = {
			id: 'kwaipilot.sendMessage',
			name: '发送消息',
			description: '向 KwaiPilot 聊天界面发送消息',
			handler: this.handleSendMessage.bind(this),
			context: this
		};

		const isSendMessageRegistered = this.sideActionService.registerAction(sendMessageAction);
		console.log('Send message action registered:', isSendMessageRegistered);

		// 示例2: 注册打开设置操作
		const openSettingsAction: ISidebarAction = {
			id: 'kwaipilot.openSettings',
			name: '打开设置',
			description: '打开 KwaiPilot 设置面板',
			handler: this.handleOpenSettings.bind(this),
			context: this
		};

		const isOpenSettingsRegistered = this.sideActionService.registerAction(openSettingsAction);
		console.log('Open settings action registered:', isOpenSettingsRegistered);

		// 示例3: 注册刷新操作
		const refreshAction: ISidebarAction = {
			id: 'kwaipilot.refresh',
			name: '刷新界面',
			description: '刷新 KwaiPilot 界面',
			handler: this.handleRefresh.bind(this),
			context: this
		};

		const isRefreshRegistered = this.sideActionService.registerAction(refreshAction);
		console.log('Refresh action registered:', isRefreshRegistered);
	}

	/**
	 * 事件监听示例
	 */
	private setupEventListeners(): void {
		// 监听UI挂载状态变化
		this.sideActionService.onDidChangeMountState((mountInfo: ISidebarMountInfo) => {
			console.log('Mount state changed:', {
				uiType: mountInfo.uiType,
				mounted: mountInfo.mounted,
				timestamp: mountInfo.timestamp
			});

			if (mountInfo.mounted) {
				console.log(`UI ${mountInfo.uiType} has been mounted`);
				// 可以在这里执行一些初始化操作
			} else {
				console.log(`UI ${mountInfo.uiType} has been unmounted`);
				// 可以在这里执行一些清理操作
			}
		});

		// 监听操作注册变化
		this.sideActionService.onDidChangeActions((action: ISidebarAction) => {
			console.log('Action changed:', {
				id: action.id,
				name: action.name,
				description: action.description
			});
		});
	}

	/**
	 * 执行操作示例
	 */
	async executeActionsExample(): Promise<void> {
		try {
			// 执行发送消息操作
			const messageResult = await this.sideActionService.executeAction(
				'kwaipilot.sendMessage',
				'Hello, KwaiPilot!',
				{ type: 'user', urgent: false }
			);
			console.log('Send message result:', messageResult);

			// 执行打开设置操作
			const settingsResult = await this.sideActionService.executeAction(
				'kwaipilot.openSettings',
				'general'
			);
			console.log('Open settings result:', settingsResult);

			// 执行刷新操作
			const refreshResult = await this.sideActionService.executeAction('kwaipilot.refresh');
			console.log('Refresh result:', refreshResult);

		} catch (error) {
			console.error('Failed to execute action:', error);
		}
	}

	/**
	 * 获取服务状态示例
	 */
	getServiceStatusExample(): void {
		const status = this.sideActionService.getServiceStatus();
		console.log('Service status:', {
			isUIMounted: status.isUIMounted,
			registeredActionCount: status.registeredActionCount,
			mountedUI: status.mountedUI ? {
				uiType: status.mountedUI.uiType,
				mounted: status.mountedUI.mounted
			} : null,
			actions: status.registeredActions.map(action => ({
				id: action.id,
				name: action.name
			}))
		});
	}

	/**
	 * 清理示例
	 */
	cleanupExample(): void {
		// 卸载UI
		const uiUnmounted = this.sideActionService.unmountUI();
		console.log('UI unmounted:', uiUnmounted);

		// 注销特定操作
		const actionUnregistered = this.sideActionService.unregisterAction('kwaipilot.sendMessage');
		console.log('Send message action unregistered:', actionUnregistered);

		// 可选: 清空所有注册的操作 (谨慎使用)
		// this.sideActionService.clearAllActions();
	}

	// ==================== 操作处理函数 ====================

	/**
	 * 处理发送消息操作
	 */
	private async handleSendMessage(message: string, options?: any): Promise<{ success: boolean; messageId: string }> {
		console.log('Handling send message:', { message, options });

		// 这里实现实际的发送消息逻辑
		// 例如调用聊天服务、更新UI等

		// 模拟异步操作
		await new Promise(resolve => setTimeout(resolve, 100));

		return {
			success: true,
			messageId: `msg_${Date.now()}`
		};
	}

	/**
	 * 处理打开设置操作
	 */
	private async handleOpenSettings(section?: string): Promise<{ opened: boolean; section: string }> {
		console.log('Handling open settings:', { section });

		// 这里实现实际的打开设置逻辑
		// 例如显示设置面板、导航到特定section等

		return {
			opened: true,
			section: section || 'general'
		};
	}

	/**
	 * 处理刷新操作
	 */
	private async handleRefresh(): Promise<{ refreshed: boolean; timestamp: number }> {
		console.log('Handling refresh');

		// 这里实现实际的刷新逻辑
		// 例如重新加载数据、更新UI状态等

		return {
			refreshed: true,
			timestamp: Date.now()
		};
	}
}

/**
 * 如何在其他类中使用 SideActionService
 *
 * 下面是一个完整的使用示例:
 */
export class KwaiPilotSidebarComponent {

	constructor(
		// 通过依赖注入获取服务
		private readonly sideActionService: ISideActionService
	) {
		this.initialize();
	}

	private initialize(): void {
		// 1. 挂载组件UI
		this.mountComponentUI();

		// 2. 注册组件操作
		this.registerComponentActions();

		// 3. 设置事件监听
		this.setupEventHandlers();
	}

	private mountComponentUI(): void {
		const mounted = this.sideActionService.mountUI({
			uiType: 'sidebar-component',
			params: {
				title: 'KwaiPilot Assistant',
				collapsible: true,
				resizable: true
			}
		});

		if (mounted) {
			console.log('KwaiPilot sidebar component UI mounted successfully');
		}
	}

	private registerComponentActions(): void {
		// 注册多个操作
		const actions: ISidebarAction[] = [
			{
				id: 'kwaipilot.sidebar.toggle',
				name: '切换侧边栏',
				handler: () => this.toggleSidebar()
			},
			{
				id: 'kwaipilot.sidebar.expand',
				name: '展开侧边栏',
				handler: () => this.expandSidebar()
			},
			{
				id: 'kwaipilot.sidebar.collapse',
				name: '折叠侧边栏',
				handler: () => this.collapseSidebar()
			}
		];

		actions.forEach(action => {
			this.sideActionService.registerAction(action);
		});
	}

	private setupEventHandlers(): void {
		// 监听UI状态变化
		this.sideActionService.onDidChangeMountState((mountInfo) => {
			this.handleUIStateChange(mountInfo);
		});
	}

	private handleUIStateChange(mountInfo: ISidebarMountInfo): void {
		if (mountInfo.mounted) {
			console.log('Sidebar UI is now mounted, initializing...');
			// 执行初始化逻辑
		} else {
			console.log('Sidebar UI is unmounted, cleaning up...');
			// 执行清理逻辑
		}
	}

	// 组件操作方法
	private toggleSidebar(): boolean {
		console.log('Toggling sidebar');
		return true;
	}

	private expandSidebar(): boolean {
		console.log('Expanding sidebar');
		return true;
	}

	private collapseSidebar(): boolean {
		console.log('Collapsing sidebar');
		return true;
	}

	// 清理方法
	dispose(): void {
		this.sideActionService.unmountUI();
		this.sideActionService.unregisterAction('kwaipilot.sidebar.toggle');
		this.sideActionService.unregisterAction('kwaipilot.sidebar.expand');
		this.sideActionService.unregisterAction('kwaipilot.sidebar.collapse');
	}
}
