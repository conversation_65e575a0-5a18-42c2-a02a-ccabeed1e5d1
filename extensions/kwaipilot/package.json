{"name": "kwai<PERSON>lot", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "一款为快手程序员量身打造的、更懂快手代码的代码大模型，集合前沿技术，为您的编程工作提供支持，成为您编程路上的得力助手！", "version": "9.4.3", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "icon": "assets/images/kwaipilot-logo1.png", "homepage": "https://kwaipilot.corp.kuaishou.com/", "repository": "https://kwaipilot.corp.kuaishou.com/", "keywords": ["code-suggestion", "copilot", "code-inference"], "engines": {"vscode": "^1.77.0"}, "bugs": {}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://kwaipilot.corp.kuaishou.com/"}, "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "license": "<PERSON><PERSON><PERSON><PERSON>", "categories": ["Other"], "activationEvents": ["*"], "main": "./out/extension-export.js", "contributes": {"commands": [{"command": "kwaipilot.showInlineCompletion", "title": "Kwaipilot：触发内联补全"}, {"command": "kwaipilot.newComposer", "title": "打开代码智能体"}, {"command": "kwaipilot.addToComposerContext", "title": "将选中区域添加到智能体", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.addFileToContext", "title": "将文件添加到上下文", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.generateCommentMessage", "title": "Kwaipilot：生成代码注释", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.generateUnitTest", "title": "Kwaipilot：生成单元测试", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.explain", "title": "Kwaipilot：解释代码", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.home", "title": "前往问答引擎", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/home-dark.svg", "light": "assets/images/home-light.svg"}}, {"command": "kwaipilot.feedback", "title": "用户反馈", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/zmn-dark.svg", "light": "assets/images/zmn-light.svg"}}, {"command": "kwaipilot.help", "title": "帮助文档", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/help-dark.svg", "light": "assets/images/help-light.svg"}}, {"command": "kwaipilot.settings", "title": "设置", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/settings-dark.svg", "light": "assets/images/settings-light.svg"}}, {"command": "kwaipilot.reloadWebview", "title": "刷新 webview", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.quickAsk", "title": "Kwaipilot：快速问答", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.showDiff", "title": "Kwaipilot：显示代码块", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.fileDiff", "title": "Kwaipilot：单文件中显示文件差异", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.addDataAnnotation", "title": "Kwaipilot：数据增强标注", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.handleEscape", "title": "Kwaipilot：处理Esc键"}, {"command": "kwaipilot.handleTab", "title": "Kwaipilot：处理Tab键"}, {"command": "kwaipilot.openCodeIndexManagement", "title": "Kwaipilot：代码索引管理", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openRulesManagement", "title": "Kwaipilot：规则管理", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openBasicsManagement", "title": "Kwaipilot：基础配置", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openFunctionManagement", "title": "Kwaipilot：功能配置", "category": "kwai<PERSON>lot"}, {"command": "kwaiPilot.reportBadCase", "title": "Kwaipilot：上报badcase", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.acceptDiff", "title": "接受更改", "category": "kwai<PERSON>lot", "icon": "$(check)"}, {"command": "kwaipilot.rejectDiff", "title": "拒绝更改", "category": "kwai<PERSON>lot", "icon": "$(close)"}, {"command": "kwaipilot.nextDiffFile", "title": "下一个差异文件", "category": "kwai<PERSON>lot", "icon": "$(arrow-right)"}, {"command": "kwaipilot.previousDiffFile", "title": "上一个差异文件", "category": "kwai<PERSON>lot", "icon": "$(arrow-left)"}, {"command": "kwaipilot.lineAccept", "title": "接受续写当前行", "category": "kwai<PERSON>lot"}], "submenus": [{"id": "kwaipilot_submenus", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "kwaipilot_submenus_files", "label": "<PERSON><PERSON><PERSON><PERSON>"}], "menus": {"editor/context": [{"command": "kwaipilot.addToComposerContext", "group": "1_kwaipilot@1"}, {"command": "kwaipilot.addFileToContext", "group": "1_kwai<PERSON>lot@2", "when": "resourceScheme == file"}, {"command": "kwaipilot.generateCommentMessage", "group": "1_kwaipilot@4", "when": "editorHasSelection"}, {"command": "kwaipilot.generateUnitTest", "group": "1_kwaipilot@5", "when": "editorHasSelection"}, {"command": "kwaipilot.addDataAnnotation", "when": "kwaipilot.canDataAnnotation", "group": "1_kwaipilot@6"}], "explorer/context": [{"command": "kwaipilot.quickAsk", "group": "1_kwaipilot@1"}, {"command": "kwaipilot.addFileToContext", "group": "1_kwai<PERSON>lot@2", "when": "resourceScheme == file"}], "kwaipilot_submenus_files": [{"command": "kwaipilot.quickAsk", "group": "group1@1"}], "kwaipilot_submenus": [{"command": "kwaipilot.addToComposerContext", "group": "group1@1"}, {"command": "kwaipilot.addFileToContext", "group": "group1@2", "when": "resourceScheme == file"}, {"command": "kwaipilot.generateCommentMessage", "group": "group2@1", "when": "editorHasSelection"}, {"command": "kwaipilot.generateUnitTest", "group": "group2@2", "when": "editorHasSelection"}, {"command": "kwaipilot.addDataAnnotation", "when": "kwaipilot.canDataAnnotation", "group": "group2@3"}], "view/title": [{"command": "kwaipilot.feedback", "when": "view == kwaiPilotChatWebView", "group": "navigation@2"}, {"command": "kwaipilot.help", "when": "view == kwaiPilotChatWebView", "group": "navigation@3"}, {"command": "kwaipilot.settings", "when": "view == kwaiPilotChatWebView", "group": "navigation@4"}, {"command": "kwaipilot.reloadWebview", "when": "view == kwaiPilotChatWebView && kwaipilot.isDeveloperMode === true", "group": "navigation@7"}], "editor/title/context": [{"command": "kwaipilot.addFileToContext", "group": "1_kwaipilot@1", "when": "resourceScheme == file"}]}, "keybindings": [{"command": "kwaipilot.handleEscape", "key": "escape", "when": "editorTextFocus && kwaipilot.prediction.isVisible"}, {"command": "kwaipilot.handleTab", "key": "tab", "when": "editorTextFocus && kwaipilot.prediction.isVisible"}, {"command": "kwaipilot.showInlineCompletion", "key": "alt+p", "mac": "alt+p", "when": "editorTextFocus"}, {"command": "kwaipilot.addToComposerContext", "key": "cmd+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "kwaipilot.addFileToContext", "when": "editorTextFocus"}, {"command": "kwaipilot.acceptDiff", "mac": "shift+cmd+enter", "key": "shift+ctrl+enter"}, {"command": "kwaipilot.rejectDiff", "mac": "cmd+z", "key": "ctrl+z", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.lineAccept", "key": "ctrl+down", "mac": "cmd+down", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "kwaipilot.rejectDiff", "mac": "shift+cmd+backspace", "key": "shift+ctrl+backspace"}, {"command": "kwaipilot.acceptVerticalDiffBlock", "mac": "cmd+y", "key": "ctrl+y", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.rejectVerticalDiffBlock", "mac": "cmd+n", "key": "ctrl+n", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.generateCommentMessage", "key": "alt+ctrl+m", "mac": "ctrl+cmd+m", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaipilot.generateUnitTest", "key": "alt+ctrl+t", "mac": "ctrl+cmd+t", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaipilot.addDataAnnotation", "key": "ctrl+m", "mac": "cmd+m", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaiPilot.reportBadCase", "key": "ctrl+u", "mac": "cmd+u", "when": "editor<PERSON><PERSON><PERSON>"}]}}